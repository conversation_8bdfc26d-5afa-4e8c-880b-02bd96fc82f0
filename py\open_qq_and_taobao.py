import os
import time
import pyautogui

# 指定exe文件的路径
exe_path = r"E:\Program Files (x86)\Tencent\QQBrowser\QQBrowser.exe"  # 请将此路径替换为你想要打开的exe文件的实际路径

# 打开exe文件
os.startfile(exe_path)  # 这个方法仅适用于Windows系统

# 如果需要为特定的应用程序执行额外的操作，可以添加额外的代码
# 例如：等待应用程序启动
# time.sleep(5)  # 等待5秒

# 地址栏输入taobao.com并回车
time.sleep(2)  # 等待应用程序启动
pyautogui.hotkey('ctrl', 'l')  # 按下Ctrl+L组合键，定位到地址栏
pyautogui.write('taobao.com')  # 输入taobao.com

pyautogui.press('enter')  # 按下回车键 
#获取当前鼠标所在位置的坐标。 
x, y = pyautogui.position()
print(f"当前鼠标位置: ({x}, {y})")  # 打印当前鼠标位置
pyautogui.click(x, y)   
# pyautogui.displayMousePosition()

# <div class="index_module_closeBtn__333f3756"><div class="index_module_cross__333f3756"></div></div>
# 定位页面元素，点击
# 这里假设你需要点击一个特定的按钮或链接
# 你可以使用pyautogui的其他方法来定位和点击元素
# 例如，点击某个特定位置的按钮
# pyautogui.click(x=100, y=200)  # 替换为实际需要点击的坐标         .
# 或者使用图像识别来点击按钮
# pyautogui.click('button_image.png')  # 替换为实际按钮
# 注意：如果使用图像识别，需要确保按钮的图像文件存在于当前目录中。  
# 使用文本识别来点击按钮 



 
time.sleep(2)  # 等待页面加载完成



print("指定的exe文件已经启动。")