import pyautogui
import pytesseract
from PIL import Image
import time

def recognize_and_interact(target_text):
    # 设置Tesseract的安装路径（如果未加入环境变量）
    # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    # 等待一段时间确保窗口就绪
    time.sleep(2)

    # 截取整个屏幕
    screenshot = pyautogui.screenshot()

    # 使用Tesseract进行OCR识别
    recognized_text = pytesseract.image_to_string(screenshot)

    print("识别到的文本：")
    print(recognized_text)

    # 判断目标文本是否出现在识别结果中
    if target_text in recognized_text:
        print(f"找到目标文本: {target_text}")

        # 获取目标文本在屏幕上的位置（粗略定位）
        location = pyautogui.locateOnScreen('target_text_screenshot.png', confidence=0.7)

        if location:
            # 计算点击位置（中心点）
            center = pyautogui.center(location)

            # 移动鼠标并点击
            pyautogui.moveTo(center)
            pyautogui.click()

            # 输入文本（可选）
            pyautogui.typewrite('Hello, World!')

            print("已完成点击并输入文本。")
        else:
            print("无法精确定位目标文本的位置。")
    else:
        print("未找到目标文本。")

if __name__ == '__main__':
    target_text = "示例文本"  # 替换为你要查找的目标文本
    recognize_and_interact(target_text)