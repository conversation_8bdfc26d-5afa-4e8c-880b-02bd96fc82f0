import asyncio
from playwright.async_api import async_playwright
import pandas as pd

async def search_taobao():
    async with async_playwright() as p:
        # 检查是否存在已保存的浏览器状态
        try:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context(storage_state_path='login_state.json')  # 使用保存的登录状态
            page = await context.new_page()
            await page.goto('https://www.taobao.com')
        except Exception as e:
            # 如果状态文件不存在或登录过期，则重新登录并保存状态
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            await page.goto('https://www.taobao.com')

            # 点击登录按钮（假设选择器为.a-link:has-text("登录")）
            await page.click('a:has-text("登录")')

            # 等待登录表单加载
            await page.wait_for_selector('#fm-login-id')

            # 输入用户名和密码
            username = '13659865772'
            password = '16868che68@'
            await page.fill('#fm-login-id', username)
            await page.fill('#fm-login-password', password)

            # 提交登录表单
            await page.click('button:has-text("登录")')

            # 滑动验证码（如果有）
            # 等待滑块容器出现
            # <span class="nc-lang-cnt" data-nc-lang="SLIDE" style="">向右滑动验证</span>
            await page.wait_for_selector('.nc-lang-cnt')

            # 查找滑块元素
            slider = await page.query_selector('.nc-lang-cnt')  # 假设这是滑块元素的选择器

            # 获取滑块的位置信息
            box = await slider.bounding_box()

            # 计算拖动的偏移量，假设需要向右拖动300像素
            target_x = box['x'] + 300
            current_x = box['x']

            # 模拟拖动过程，分多次小步移动以更接近真实操作
            while current_x < target_x:
                move_step = min(10, target_x - current_x)  # 每次移动10像素或剩余距离
                current_x += move_step
                await page.mouse.move(current_x, box['y'] + box['height'] / 2)

            # 释放鼠标
            await page.mouse.up()

            # 等待验证完成
            await page.wait_for_timeout(3000)  # 等待一段时间确保验证完成

            # 等待登录完成
            await page.wait_for_timeout(5000)

            # 保存当前会话状态以便下次使用
            await context.storage_state(path='login_state.json')

        # 输入搜索关键词
        await page.fill('#q', '蓝牙键盘鼠标')

        # 点击搜索按钮
        await page.click('.btn-search')

        # 等待搜索结果加载
        await page.wait_for_selector('.item-title')

        # 提取前10条商品信息
        items = await page.query_selector_all('.item-title')[:10]
        prices = await page.query_selector_all('.price')[:10]
        shops = await page.query_selector_all('.shop-name')[:10]
        buyers = await page.query_selector_all('.deal-cnt')[:10]

        data = []
        for i in range(len(items)):
            item = await items[i].text_content()
            price = await prices[i].text_content()
            shop = await shops[i].text_content()
            buyer = await buyers[i].text_content()
            data.append({
                'title': item,
                '店铺': shop,
                '付款人数': buyer,
                '价格': price
            })

        df = pd.DataFrame(data)
        df.to_excel('taobao_products.xlsx', index=False)
        print(df)

        await browser.close()

if __name__ == '__main__':
    asyncio.run(search_taobao())
