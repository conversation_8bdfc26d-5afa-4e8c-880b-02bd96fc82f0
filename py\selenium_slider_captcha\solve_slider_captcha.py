from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
import time

def solve_slider_captcha():
    # 初始化浏览器驱动（这里以Chrome为例）
    driver = webdriver.Chrome()  # 确保chromedriver在PATH中或指定路径
    
    # 打开包含滑块验证码的页面（替换为实际的目标页面）
    driver.get('https://example.com/login-with-slider-captcha')  # 替换为实际网址
    
    try:
        # 等待滑块元素出现
        slider_element = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'slider-button'))  # 替换为实际滑块元素的选择器
        )
        
        # 模拟拖动滑块到终点位置
        success = False
        retry_count = 0
        max_retries = 3

        while not success and retry_count < max_retries:
            try:
                actions = ActionChains(driver)
                actions.click_and_hold(slider_element).move_by_offset(300, 0)
                actions.release().perform()

                # 等待验证结果
                time.sleep(2)

                # 检查是否出现验证失败提示（例如：UN4qn4）
                if not WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.error-message:contains("UN4qn4")'))  # 示例选择器
                ):
                    success = True
                    print("滑块验证成功。")
                else:
                    print(f"验证失败，第 {retry_count + 1} 次重试...")
                    retry_count += 1
                    time.sleep(2)

            except Exception as e:
                print(f"处理滑块验证时发生错误: {e}")
                break

        if not success:
            print("多次尝试滑块验证失败，请检查页面逻辑或手动操作。")

    except Exception as e:
        print(f"处理滑块验证时发生错误: {e}")
    
    # 关闭浏览器
    driver.quit()

if __name__ == '__main__':
    solve_slider_captcha()