import pyautogui
import pytesseract
from PIL import Image
import requests
import time

#====== query_ollama 函数用于向本地Ollama服务发送请求并获取响应
def query_ollama(prompt):
    """
    向本地Ollama服务发送请求，获取模型响应。
    假设Ollama运行在本地 http://localhost:11434
    """
    url = "http://localhost:11434/api/generate"
    payload = {
        "model": "llama3",
        "prompt": prompt,
        "stream": False
    }
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json().get('response', '')
    else:
        print("Ollama请求失败")
        return None

# =======recognize_screen_text 函数用于截取屏幕并识别文本
def recognize_screen_text():
    """
    截图并使用Tesseract OCR识别屏幕上的文本。
    """
    # 使用pyautogui库截取屏幕上的图像
    screenshot = pyautogui.screenshot()
    # 使用pytesseract库将图像转换为文本
    text = pytesseract.image_to_string(screenshot)
    # 返回文本，并去除首尾的空格
    return text.strip()

#========== 结合自然语言指令与屏幕信息进行交互
def interact_based_on_instruction(instruction):
    """
    根据自然语言指令，结合屏幕信息与Ollama模型进行交互。
    """
    # 获取当前屏幕文本
    screen_text = recognize_screen_text()
    print("识别到的屏幕内容：\n", screen_text)

    # 构建提示词，让模型判断应采取什么动作
    prompt = f"""
当前屏幕上识别到的内容如下：
{screen_text}

用户希望执行以下操作：
{instruction}

请分析是否可以在当前界面上执行该操作，并给出具体坐标或动作建议。
例如：点击‘登录’按钮、在搜索框输入关键词等。
"""

    model_response = query_ollama(prompt)
    if model_response:
        print("模型建议：\n", model_response)
        # 此处可扩展为自动解析模型输出并执行具体 pyautogui 动作
        # 示例：硬编码点击某个位置
        pyautogui.click(x=100, y=100)
        print("已根据模型建议执行点击操作。")
    else:
        print("未能从模型获取有效响应。")

if __name__ == '__main__':
    user_instruction = "请找到搜索框并输入‘蓝牙键盘鼠标’"
    interact_based_on_instruction(user_instruction)