import sys
sys.path.append(r'E:\pak\py')

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service  # 添加该行导入Service
from selenium.webdriver.chrome.options import Options  # 添加该行导入Options

import time
import os
import requests
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

print(sys.path)
# 创建目标文件夹
target_dir = r'E:\Doc\onedriver\OneDrive\桌面\wb'
# 定义搜索URL
search_url = "https://www.doubao.com/chat"
keywords = "王安宇代言的卫龙魔芋爽 蓝色礼盒"

url = search_url  
os.makedirs(target_dir, exist_ok=True)

# 设置Chrome驱动路径（请根据实际情况修改）
chrome_driver_path = r'E:\Program Files\chromedriver-win64\chromedriver.exe'
 
  
# 启动浏览器
# service = Service(chrome_driver_path)
# options = webdriver.ChromeOptions()
# options.add_argument('--headless')  # 可选：无头模式运行
# driver = webdriver.Chrome(service=service, options=options)

service = Service(chrome_driver_path)
options = Options()
# options.add_argument("--headless=new")
# options.add_argument('--disable-gpu')  # 禁用GPU加速
driver = webdriver.Chrome(service=service, options=options)


try:
    driver.get(url)
    
    # 等待页面加载完成（可以根据需要调整等待时间）
    time.sleep(2)
 
    # # 查找包含文本“新对话”的 div
    # new_conversation_btn = WebDriverWait(driver, 10).until(
    #     EC.presence_of_element_located((By.XPATH, '//div[text()="新对话"]'))
    # )
    # # 滚动到元素可见
    # driver.execute_script("arguments[0].scrollIntoView(true);", new_conversation_btn)
    # # 使用JavaScript点击元素
    # driver.execute_script("arguments[0].click();", new_conversation_btn)
    # new_conversation_btn = driver.find_element(By.XPATH, '//div[text()="新对话"]')
    # print('新对话按钮已点击')
    # print(new_conversation_btn.text)
    # new_conversation_btn.click()
    time.sleep(2)
    
    # el = WebDriverWait(driver, 10).until(
    #     driver.find_element(By.XPATH, '//div[contains(@class, "bottom-eXKPXS")]')
    # )
    print('1111111111111')
    # print(el.text)
    # new_conversation_btn.click()
    time.sleep(2)
    
    
    
    # 查找包含文本“图片”的 div
    btn_img = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, '//div[contains(text(), "图像")]'))
    )
    print(btn_img)
    driver.execute_script("arguments[0].click();", btn_img)
    time.sleep(2)
   
    # 选中文本框框输入内容
    input_box = WebDriverWait(driver, 30).until(
        EC.presence_of_element_located((By.XPATH, '//div[contains(@role, "textbox")]'))
    )
    input_box.send_keys(keywords)
    time.sleep(2)
    
    # 点键盘回车键
    input_box.send_keys(Keys.RETURN)
    time.sleep(10)
    
    #下载图片 
    # 直接查找 class
    img_elements = WebDriverWait(driver, 20).until(
        EC.presence_of_all_elements_located((By.XPATH, '//img[contains(@class, "image-Lgfrf0")]' ))
    )
    print(img_elements) 
    # 提取图片链接
    img_urls = [img.get_attribute('src') for img in img_elements if img.get_attribute('src')]
    
    # 下载并保存前3张图片
    for i, img_url in enumerate(img_urls[:3]):
        print(img_url)
        try:
            img_data = requests.get(img_url).content
            with open(os.path.join(target_dir, f'image_{i}.jpg'), 'wb') as handler:
                handler.write(img_data)
            print(f'已下载第{i+1}张图片')
        except Exception as e:
            print(f'下载第{i+1}张图片失败: {e}')
            
finally:
    print('111')
    # driver.quit()

print('所有图片下载完成！')