from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import time

def solve_slider_captcha():
    # 初始化浏览器驱动（这里以Chrome为例）
    driver = webdriver.Chrome()  # 确保chromedriver在PATH中或指定路径
    
    # 打开包含滑块验证码的页面（替换为实际的目标页面）
    driver.get('https://example.com/login-with-slider-captcha')  # 替换为实际网址
    
    try:
        # 等待滑块元素出现
        slider_element = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'slider-button'))  # 替换为实际滑块元素的选择器
        )
        
        # 模拟拖动滑块到终点位置
        actions = ActionChains(driver)
        actions.click_and_hold(slider_element).move_by_offset(300, 0)  # 根据实际需要调整偏移量
        actions.release().perform()
        
        # 等待验证完成
        time.sleep(2)  # 可根据实际情况调整等待时间或使用显式等待特定元素加载
        print("滑块验证已完成。")
        
    except Exception as e:
        print(f"处理滑块验证时发生错误: {e}")
    
    # 关闭浏览器
    driver.quit()

if __name__ == '__main__':
    solve_slider_captcha()