from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import time

def search_taobao():
    
    # 设置Chrome驱动路径（请根据实际情况修改）
    chrome_driver_path = r'E:\Program Files\chromedriver-win64\chromedriver.exe'
     
    service = Service(chrome_driver_path)
    options = Options()
    # options.add_argument("--headless=new")
    # options.add_argument('--disable-gpu')  # 禁用GPU加速
    driver = webdriver.Chrome(service=service, options=options)
 
    # 打开淘宝首页
    driver.get('https://www.taobao.com')
    
    # 输入搜索关键词
    search_box = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, 'q'))
    )
    search_box.send_keys('蓝牙键盘鼠标')
    
    # 点击搜索按钮
    search_button = driver.find_element(By.CLASS_NAME, 'btn-search')
    search_button.click()
    
    # 等待搜索结果加载
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, 'item-title'))
    )
    
    # 滚动到底部以加载所有商品
    last_height = driver.execute_script("return document.body.scrollHeight")
    while True:
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)  # 等待页面加载
        new_height = driver.execute_script("return document.body.scrollHeight")
        if new_height == last_height:
            break
        last_height = new_height
    
    # 提取前10条商品信息
    items = driver.find_elements(By.CLASS_NAME, 'item-title')[:10]
    prices = driver.find_elements(By.CLASS_NAME, 'price')[:10]
    shops = driver.find_elements(By.CLASS_NAME, 'shop-name')[:10]
    buyers = driver.find_elements(By.CLASS_NAME, 'deal-cnt')[:10]
    
    data = []
    for i in range(len(items)):
        item = items[i].text
        price = prices[i].text
        shop = shops[i].text
        buyer = buyers[i].text
        data.append({
            'title': item,
            '店铺': shop,
            '付款人数': buyer,
            '价格': price
        })
    
    df = pd.DataFrame(data)
    df.to_excel('taobao_products_selenium.xlsx', index=False)
    print(df)
    
    # 关闭浏览器
    driver.quit()

if __name__ == '__main__':
    search_taobao()