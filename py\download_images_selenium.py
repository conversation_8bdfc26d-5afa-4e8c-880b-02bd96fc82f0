import sys
sys.path.append(r'E:\pak\py')

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

import time
import os
import requests
 

print(sys.path)
# 创建目标文件夹
target_dir = r'E:\Doc\onedriver\OneDrive\桌面\wb'
# 定义搜索URL
search_url = "https://image.baidu.com/search/index?tn=baiduimage&ps=1&ct=201326592&lm=-1&cl=2&nc=1&ie=utf-8&word="
keywords = "王安宇代言的卫龙魔芋爽 蓝色礼盒"

# 设置Chrome驱动路径（请根据实际情况修改）
chrome_driver_path = r'E:\Program Files\chromedriver-win64\chromedriver.exe'


url = search_url + keywords
os.makedirs(target_dir, exist_ok=True)
  
# 启动浏览器
# service = Service(chrome_driver_path)
# options = webdriver.ChromeOptions()
# options.add_argument('--headless')  # 可选：无头模式运行
# driver = webdriver.Chrome(service=service, options=options)

service = Service(chrome_driver_path)
options = Options()
# options.add_argument('--headless')
options.add_argument('--disable-gpu')  # 禁用GPU加速
driver = webdriver.Chrome(service=service, options=options)

# 最大化窗口
driver.maximize_window()

try:
    driver.get(url)
    
    # 等待页面加载完成（可以根据需要调整等待时间）
    time.sleep(5)
 
    # 等待元素可见并可点击
    new_conversation_btn = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, '//div[text()="新对话"]'))
    )
    new_conversation_btn.click()
except Exception as e:
    print(f"元素交互时发生错误: {e}")
finally:
    driver.quit()

print('所有图片下载完成！')